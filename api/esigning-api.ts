import FormData from 'form-data';
import constants from '../src/_helpers/constants';
import axios from "axios";

export type InternalSignResponse = {
  code?: number;
  msg?: string;
  data?: any;
};

export async function internalEsign(params: {contractNumber: string; type: string, buffer: Buffer, fileName: string, partnerCode: string}): Promise<InternalSignResponse>  {
    const {contractNumber, type, buffer, fileName, partnerCode} = params;
    const url = constants.HOST.BSS_ESIGNING_SERVICE + `/esigning/internal/${partnerCode}/sign`;

  const formData = new FormData();
  formData.append("file", buffer, fileName);
  formData.append("contractNumber", contractNumber);
  formData.append("type", type);

    try {
        const response = await axios.post(url, formData, {
            headers: {
                ...formData.getHeaders(),
            },
        });
        return response.data;
    } catch (error) {
        console.error("Error:", error.message);
        throw error;
    }
}