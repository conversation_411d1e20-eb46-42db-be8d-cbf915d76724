import constants from "../src/_helpers/constants";
import axios from "axios";

export type InternalSignResponse = {
  code?: number;
  msg?: string;
  data?: any;
};

export async function internalCallBack(params: { contract_number: string; callback_url: string }): Promise<InternalSignResponse> {
  const { contract_number, callback_url } = params;

  try {
    // const url = constants.HOST.MC_LOS_SERVICE + `/los-mc-credit/ui/lender/v1/af3/handler-documents-generated`;
    // const allowedHosts = Object.values(constants.HOST);
    // const parsedUrl = new URL(callback_url);
    const uri = callback_url || "/los-mc-credit/ui/lender/v1/af3/handler-documents-generated";
    const url = `${global.basic!.losMcCredit[process.env.HOST_ENV]}${uri}`;

    // Strict host validation
    // if (!allowedHosts.some((host) => parsedUrl.origin === host || parsedUrl.origin.startsWith(host))) {
    //   console.log("URL not in whitelist, skipping callback document generation");
    //   return null;
    // }

    // Safe: POST directly to validated callback_url
    const result = await axios.post(url, { contract_number });
    console.log(`invoke callback url:${url} with contract_number: ${contract_number}`);
    if (result) {
      return result?.data;
    } else {
      return null;
    }
  } catch (error) {
    console.error("Error:", error.message);
    return null;
  }
}
