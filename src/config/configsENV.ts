export default {
  DATABASE: {
    host: process.env.DB_HOST_WRITE,
    port: parseInt(process.env.DB_PORT),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    schema: process.env.DB_SCHEMA || process.env.DB_DATABASE,
  },
  DATABASE_LOS: {
    host_los: process.env.DB_HOST_WRITE_LOS_MC,
    port_los: parseInt(process.env.DB_PORT_LOS_MC),
    user_los: process.env.DB_USER_LOS_MC,
    password_los: process.env.DB_PASSWORD_LOS_MC,
    database_los: process.env.DB_DATABASE_LOS_MC,
    schema_los: process.env.DB_SCHEMA_LOS_MC || process.env.DB_DATABASE_LOS_MC,
  },
  DATABASE_LOS_UNITED: {
    hostLosUnited: process.env.DB_HOST_WRITE_LOS_UNITED,
    portLosUnited: parseInt(process.env.DB_PORT_LOS_UNITED),
    userLosUnited: process.env.DB_USER_LOS_UNITED,
    passwordLosUnited: process.env.DB_PASSWORD_LOS_UNITED,
    databaseLosUnited: process.env.DB_DATABASE_LOS_UNITED,
    schemaLosUnited: process.env.DB_SCHEMA_LOS_UNITED || process.env.DB_DATABASE_LOS_UNITED,
  },
  REDIS: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT),
  },
  SERVICE_URL: process.env.SERVICE_URL,
  PORT: process.env.PORT,
};
