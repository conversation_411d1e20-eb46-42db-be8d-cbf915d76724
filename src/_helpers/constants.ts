require("dotenv").config({ path: `.env.${process.env.NODE_ENV}` });

const constants = {
  CONFIG_URL: {
    PREFIX_URL: process.env.PREFIX_URL,
  },
  CONFIG_S3: {
    S3_ACCESS_KEY_ID: process.env.S3_ACCESS_KEY_ID,
    S3_SECRET_ACCESS_KEY: process.env.S3_SECRET_ACCESS_KEY,
    S3_REGION: process.env.S3_REGION,
    S3_BUCKET_NAME: process.env.S3_BUCKET_NAME,
  },
  HOST: {
    BSS_ESIGNING_SERVICE: process.env.BSS_ESIGNING_SERVICE,
    MC_LOS_SERVICE: process.env.MC_LOS_SERVICE,
  },
  API_CODE_SUCCESS: 0,
  API_CODE_ERROR: 1,
  API_TEXT_SUCCESS: "Success",
  TOKEN_SERVICE: "Basic ZHNhX21vYmlsZV9jbGllbnQ6dGU0dk9xeEowWUFqY3Fmd3ZsQWhONThsR1ZsYnBvd7s=",
  TASK_STATUSES: {
    INIT: 0,
    PROCESSING: 1,
    SUCCESS: 2,
    ERROR: -1,
  },
  TASK_NAMES: {
    GEN_EVC_CONTRACT_FILE: "GEN_EVC_CONTRACT_FILE",
    GEN_EVC_ESIGNING_CONTRACT_FILE: "GEN_EVC_ESIGNING_CONTRACT_FILE",
    GEN_EVC_TERM_POLICY: "GEN_EVC_TERM_POLICY",
    GEN_DNSE_TTRV: "GEN_DNSE_TTRV",
    GEN_DNSE_BCTD: "GEN_DNSE_BCTD",
    GEN_DNSE_LD: "GEN_DNSE_LD",
    GEN_GIMO_BCTD: "GEN_GIMO_BCTD",
    GEN_FUND_BCTD: "GEN_FUND_BCTD",
    GEN_GIMO_LD: "GEN_GIMO_LD",
    GEN_FUND_LD: "GEN_FUND_LD",
    GEN_FUND_DDH: "GEN_FUND_DDH",
    GEN_CUSTOMER_ESIGNING_FILE: "GEN_CUSTOMER_ESIGNING_FILE", //gen file cho kh ky
    GEN_MANF_BCTD: "GEN_MANF_BCTD",
    GEN_MANF_LD: "GEN_MANF_LD",
    GEN_MANF_ESIGNING_CONTRACT_FILE: "GEN_MANF_ESIGNING_CONTRACT_FILE",
    GEN_TERM_POLICY_V2: "GEN_TERM_POLICY_V2",
    GEN_BTT_CONTRACT_FILE: "GEN_BTT_CONTRACT_FILE",
    GEN_BTT_B03_GIAY_DE_NGHI_UNG_TRUOC_KIEM_KUNN: "GEN_BTT_B03_GIAY_DE_NGHI_UNG_TRUOC_KIEM_KUNN",
    GEN_BIZZ_BCTD: "GEN_BIZZ_BCTD",
  },
  LOS_TYPE: {
    MC_LOS: "MC_LOS",
    LOS_UNITED: "LOS_UNITED",
  },
  SIGN_STATUS: {
    INIT: 0,
    FIRST_SIGN: 1,
    SECOND_SIGN: 2,
  },
  IGNORE_LOG_PATHS: ["customer/upload-signed"],
};

export default constants;
