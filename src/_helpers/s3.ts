const AWS = require('aws-sdk')
import constants from "../_helpers/constants";
const moment = require("moment");

export async function upload(fileName, buffer, prefixDocIdPath): Promise<any> {
    return new Promise((resolve, reject) => {
        try {
            AWS.config.update({
                accessKeyId: constants.CONFIG_S3.S3_ACCESS_KEY_ID,
                secretAccessKey: constants.CONFIG_S3.S3_SECRET_ACCESS_KEY,
            })
            let bucketName = constants.CONFIG_S3.S3_BUCKET_NAME
            let s3 = new AWS.S3({region: constants.CONFIG_S3.S3_REGION})
            let acl = 'public-read';

            const param = {
                Bucket: bucketName,
                ACL: acl,
                Key: prefixDocIdPath.replace('/', '') + '/' + moment().format('yyyyMMDD') + '/' + fileName,
                Body: buffer
            }
            s3.upload(param, {}, async (err, data) => {
                if (err) {
                    reject(err)
                }
                console.log('Upload to S3 success, URL: ', data.Location)
                resolve(data)
            })
        }
        catch(error) {
            console.log(error)
            console.log(reject)
        }
    })
}



export async function download(fileLocation: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
        try {
            AWS.config.update({
                accessKeyId: constants.CONFIG_S3.S3_ACCESS_KEY_ID,
                secretAccessKey: constants.CONFIG_S3.S3_SECRET_ACCESS_KEY,
            });

            const s3 = new AWS.S3({ region: constants.CONFIG_S3.S3_REGION });

            // Tách bucket và key từ URL
            const url = new URL(fileLocation);
            const bucketName = constants.CONFIG_S3.S3_BUCKET_NAME
            const key = url.pathname.substring(1); // Bỏ dấu `/` ở đầu path

            const params = { Bucket: bucketName, Key: key };

            s3.getObject(params, (err, data) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('Download from S3 success');
                    resolve(data.Body as Buffer);
                }
            });
        } catch (error) {
            console.log(error);
            reject(error);
        }
    });
}
