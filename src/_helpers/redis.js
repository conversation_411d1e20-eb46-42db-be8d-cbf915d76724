import configsENV from "../config/configsENV";
import { createClient } from "redis";
let client = null;

initialize();

async function initialize() {
    try {
        const { host, port } = configsENV.REDIS;
        client = createClient({
            socket: {
                host,
                port,
            }
        });
        await client.connect();
        console.log("Connect redis success!")
    } catch (e) {
        console.log("Connect redis fail!")
        // throw e;
    }
}

export async function getCacheByKey(key) {
    if (client) {
        return await client.get(key);
    } else {
        return null;
    }
}

// Middleware to fetch data (simulated database query)
export async function setCacheByKey(key, value) {
    if (client) {
        await client.set(key, value)
        let cacheAfterSet = await getCache<PERSON>y<PERSON>ey(key);

        console.log('cache after set', cacheAfterSet)

        return true;
    } else {
        return false;
    }
}

export default client;
