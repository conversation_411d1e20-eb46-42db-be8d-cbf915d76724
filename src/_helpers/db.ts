import configsENV from "../config/configsENV";
import { Sequelize } from "sequelize";
import { DbAll } from "../types";
import { logTransactionModel } from "../log_transaction/log_transaction.model";
import { taskModel } from "../task/task.model";
import { loanContractDocumentModel } from "../los_united/loan_contract_document.model";
import { LOSLoanContractDocumentModel } from "../los/loan_contract_document.model";
import { loanUnsignDocModel } from "../los_united/loan_unsign_doc.model";
import { esigningModel } from "../esigning/esigning.model";

let db: DbAll = {};

initialize();

async function initialize() {
  // console.log("configsENV", JSON.stringify(configsENV));
  // create db if it doesn't already exist
  const { host, port, user, password, database, schema } = configsENV.DATABASE;
  const { hostLosUnited, portLosUnited, userLosUnited, passwordLosUnited, databaseLosUnited, schemaLosUnited } = configsENV.DATABASE_LOS_UNITED;
  const { host_los, port_los, user_los, password_los, database_los, schema_los } = configsENV.DATABASE_LOS;

  // // connect to db
  const sequelize = new Sequelize(database, user, password, {
    host,
    port,
    dialect: "postgres",
    schema,
    logging: false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  });

  const sequelizeLosUnited = new Sequelize(databaseLosUnited, userLosUnited, passwordLosUnited, {
    host: hostLosUnited,
    port: portLosUnited,
    dialect: "postgres",
    schema: schemaLosUnited,
    logging: false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  });
  const sequelizeLos = new Sequelize(database_los, user_los, password_los, {
    host: host_los,
    port: port_los,
    dialect: "postgres",
    schema: schema_los,
    logging: false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  });

  // init models and add them to the exported db object
  db.log_transactions = logTransactionModel(sequelize);
  db.tasks = taskModel(sequelize);
  db.loan_contract_document = loanContractDocumentModel(sequelizeLosUnited);
  db.los_loan_contract_document = LOSLoanContractDocumentModel(sequelizeLos);
  db.loan_unsign_doc = loanUnsignDocModel(sequelizeLosUnited);
  db.esigning = esigningModel(sequelize);

  db.sequelize = sequelize;

  //!!!!!!!!!!!!!NGUY HIEM!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
  //await sequelize.sync({ force: false, alter: false });
}

export default db;
