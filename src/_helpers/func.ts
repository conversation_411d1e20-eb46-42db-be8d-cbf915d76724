import { Request } from "express";
import { logger } from "../log_transaction/log_transaction.service";
import fs from "fs";
import XlsxTemplate from "xlsx-template";
import Docxtemplater from "docxtemplater";
import expressionParser from "docxtemplater/expressions.js";
import <PERSON>z<PERSON><PERSON> from "pizzip";
const s3Service = require("../_helpers/s3");
const { TemplateHandler } = require("easy-template-x");
import libre from "libreoffice-convert";
import { internalEsign } from "../../api/esigning-api";

export function getStandardResponse(errorCode: number, errorMessage: string, result?: any, req?: Request & { message?: string; responseCode?: number }) {
  if (req) {
    req.message = errorMessage;
    req.responseCode = errorCode;
  }
  logger(req.originalUrl || "", result, "Response", req);
  return {
    errorCode,
    errorMessage,
    result: result || {},
  };
}

export const generateXlsxFile = async (data: any, pathTemplate: string, outputType: any) => {
  try {
    const templateFile = fs.readFileSync(pathTemplate);
    const xlsxTemplate = new XlsxTemplate(templateFile);
    xlsxTemplate.substitute(1, data);
    return xlsxTemplate.generate({ type: outputType });
  } catch (e) {
    console.log("Xlsx render error", e.name, e.message);
    return null;
  }
};

export const generateDocxFile = async (data: any, pathTemplate: string, outputType: any): Promise<any> => {
  try {
    const templateFile = fs.readFileSync(pathTemplate);
    const zip = new PizZip(templateFile);
    const doc = new Docxtemplater(zip, {
      parser: expressionParser,
      paragraphLoop: true,
      linebreaks: true,
      nullGetter: () => "",
    });
    doc.render(data);
    return doc.getZip().generate({
      type: outputType,
      compression: "DEFLATE",
    });
  } catch (e) {
    console.log("docx render error", e.name, e.message);
  }
  return null;
};
export const docxToPdf = async (file: Buffer) => {
  return new Promise(function (resolve, reject) {
    libre.convert(file, ".pdf", undefined, (err, data) => {
      if (err) {
        reject(err);
      }
      resolve(data.toString("base64"));
    });
  });
};
export const docxToPdfPushS3 = async (buffer: Buffer, filePath: string, prefix: string, file_name: string) => {
  return new Promise<AWS.S3.ManagedUpload.SendData>(function (resolve, reject) {
    libre.convert(buffer, ".pdf", undefined, (err, done) => {
      if (err) {
        console.log("Error converting file:" + err.message);
        console.log(err);

        reject(err);
      } else {
        console.log(`done convert pdf ${filePath}`);

        console.log(`start upload s3 ${filePath}`);
        s3Service
          .upload(file_name, done, prefix)
          .then((data) => {
            console.log(`done upload s3 ${filePath}`);
            resolve(data);
          })
          .catch((error) => {
            console.log("upload s3 error: " + error.message);
            console.log(error);

            reject(error);
          });
      }
    });
  });
};
export function convert2push(filePath, contractData, prefix, file_name) {
  return new Promise(async function (resolve, reject) {
    let buffer = await processTemplate(filePath, contractData);
    if (buffer === null) {
      console.log(`tempFilePathTmp HĐTD is null`);
      return reject(null);
    }

    console.log(`start convert pdf ${filePath}`);
    libre.convert(buffer, ".pdf", undefined, (err, done) => {
      if (err) {
        console.log("Error converting file:" + err.message);
        console.log(err);

        reject(err);
      } else {
        console.log(`done convert pdf ${filePath}`);

        console.log(`start upload s3 ${filePath}`);
        s3Service
          .upload(file_name, done, prefix)
          .then((data) => {
            console.log(`done upload s3 ${filePath}`);
            resolve(data.Location);
          })
          .catch((error) => {
            console.log("upload s3 error: " + error.message);
            console.log(error);

            reject(error);
          });
      }
    });
  });
}

export function convert2pushs3(filePath, contractData, prefix, file_name): Promise<AWS.S3.ManagedUpload.SendData> {
  return new Promise(async function (resolve, reject) {
    let buffer = await processTemplate(filePath, contractData);
    if (buffer === null) {
      console.log(`tempFilePathTmp HĐTD is null`);
      return reject(null);
    }

    console.log(`start convert pdf ${filePath}`);
    libre.convert(buffer, ".pdf", undefined, (err, done) => {
      if (err) {
        console.log("Error converting file:" + err.message);
        console.log(err);

        reject(err);
      } else {
        console.log(`done convert pdf ${filePath}`);

        console.log(`start upload s3 ${filePath}`);
        s3Service
          .upload(file_name, done, prefix)
          .then((data) => {
            console.log(`done upload s3 ${filePath}`);
            resolve(data);
          })
          .catch((error) => {
            console.log("upload s3 error: " + error.message);
            console.log(error);

            reject(error);
          });
      }
    });
  });
}

export function convert2esign2push(filePath, contractData, prefix, file_name, type, partnerCode) {
  return new Promise(async function (resolve, reject) {
    let buffer = await processTemplate(filePath, contractData);
    if (buffer === null) {
      console.log(`tempFilePathTmp HĐTD is null`);
      return reject(null);
    }

    console.log(`start convert pdf ${filePath}`);
    libre.convert(buffer, ".pdf", undefined, async (err, done) => {
      if (err) {
        console.log("Error converting file:" + err.message);
        console.log(err);

        reject(err);
      } else {
        console.log(`done convert pdf ${filePath}`);

        // console.log(`start esign ${filePath}`);
        // const esignResponse = await internalEsign({
        //   contractNumber: contractData.contract_number,
        //   type: type,
        //   buffer: done,
        //   fileName: file_name,
        //   partnerCode
        // })

        console.log(`start upload s3 ${filePath}`);
        const fileBuffer = done;

        s3Service
          .upload(file_name, fileBuffer, prefix)
          .then((data) => {
            console.log(`done upload s3 ${filePath}`);
            resolve(data.Location);
          })
          .catch((error) => {
            console.log("upload s3 error: " + error.message);
            console.log(error);

            reject(error);
          });
      }
    });
  });
}

export async function processTemplate(filePath, contractData, maxDept = 35) {
  try {
    const templateFile = fs.readFileSync(filePath);
    const handler = new TemplateHandler({ maxXmlDepth: maxDept });
    const doc = await handler.process(templateFile, contractData);
    return doc;
  } catch (err) {
    console.log(`processTemplate error: ${err.message}`);
    console.log(err);
    return null;
  }
}
