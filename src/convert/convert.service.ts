import db from "../_helpers/db";
import url from "url";
import { TaskModel } from "../types";
import constants from "../_helpers/constants";
import { Op } from "sequelize";
import { convert2push, docxToPdf, generateDocxFile, generateXlsxFile } from "../_helpers/func";

const uuid = require("uuid");

export async function addConvertTask(params: { task_name: string; body: any }) {
  try {
    const { task_name, body } = params;
    console.log(`add Convert Task: ${task_name} with payload:${JSON.stringify(body)}`);
    await global._taskQueue.add({ task_name, body });

    // await db.tasks.create({
    //   task_name,
    //   body: JSON.stringify(body),
    //   status: constants.TASK_STATUSES.INIT,
    // });
  } catch (error) {
    console.log("addConvertTask err", error);
    throw "Đ<PERSON> có lỗi xảy ra!";
  }
}

// job 1 phút quét 1 lần, 1 file mất khoảng 10->20s => nên để limit 3 file/1 job
export async function executeConvertTask(params: { task_name?: string }, limit = 3) {
  if (limit == 0) {
    return;
  }

  const { task_name } = params;
  try {
    let where: any = {
      status: constants.TASK_STATUSES.INIT,
    };

    if (task_name) {
      where = {
        ...where,
        task_name: {
          [Op.in]: task_name.split(","),
        },
      };
    }

    const initTask = await db.tasks.findOne({
      where,
      order: [["id", "ASC"]],
    });

    if (initTask) {
      switch (initTask.task_name) {
        case constants.TASK_NAMES.GEN_EVC_CONTRACT_FILE:
          await doGenEvcContractFile(initTask);
          break;
        case constants.TASK_NAMES.GEN_EVC_ESIGNING_CONTRACT_FILE:
          await doGenEvcEsigningContractFile(initTask);
          break;
        case constants.TASK_NAMES.GEN_EVC_TERM_POLICY:
          await doGenEvcTermPolicy(initTask);
          break;
        case constants.TASK_NAMES.GEN_TERM_POLICY_V2:
          await doGenTermPolicyV2(initTask);
          break;
        default:
          throw `not support task ${task_name}!`;
      }

      await executeConvertTask({ task_name }, limit - 1);
    }
  } catch (error) {
    console.log("executeConvertTask err", error);
    throw "Đã có lỗi xảy ra!";
  }
}

export async function resetConvertTask(params: { status: string }) {
  const { status } = params;
  try {
    await db.tasks.update(
      {
        status: constants.TASK_STATUSES.INIT,
      },
      {
        where: { status },
      }
    );
  } catch (error) {
    console.log("resetConvertTask err", error);
    throw "Đã có lỗi xảy ra!";
  }
}

export async function generateDoc(params: { templatePath: string; data: any; outputType: string }) {
  try {
    if (params.outputType == "xlsx") {
      return generateXlsxFile(params.data, params.templatePath, "base64");
    }
    if (params.outputType == "docx") {
      return generateDocxFile(params.data, params.templatePath, "base64");
    }
    if (params.outputType == "pdf") {
      const buffer = await generateDocxFile(params.data, params.templatePath, "nodebuffer");
      return docxToPdf(buffer);
    }
  } catch (error) {
    console.log("executeConvertTask err", error);
    throw "Đã có lỗi xảy ra!";
  }
}

async function markTaskAsProcessing(task: TaskModel) {
  task.status = constants.TASK_STATUSES.PROCESSING;
  await task.save();
}

async function markTaskAsSuccess(task: TaskModel) {
  task.status = constants.TASK_STATUSES.SUCCESS;
  await task.save();
}

async function markTaskAsError(task: TaskModel, message?: string) {
  task.status = constants.TASK_STATUSES.ERROR;
  task.message = message || "";
  await task.save();
}

async function doGenEvcContractFile(task: TaskModel) {
  await markTaskAsProcessing(task);

  try {
    const taskBody = JSON.parse(task.body);
    const { file_name, contract_number, file_tem_path, file_tem_path_unsigned, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start generate file contract ${contract_number}`);

    const fileLocationSigned = await convert2push(file_tem_path, contract_data, "/los-united/signed-contract", file_name);
    let fileLocationUnsigned;
    if (file_tem_path_unsigned && file_tem_path_unsigned != file_tem_path) {
      fileLocationUnsigned = await convert2push(file_tem_path_unsigned, contract_data, "/los-united/unsigned-contract", file_name);
      if (!fileLocationUnsigned) {
        throw "convert unsigned fail!";
      }
    }
    if (!fileLocationSigned) {
      throw "convert signed fail!";
    }

    console.log(`Generate file contract ${contract_number} successfully, file location: ${fileLocationSigned}`);
    const dataSaveSignedDoc = {
      contract_number: contract_number,
      file_name: file_name,
      file_path: url.parse(fileLocationSigned.toString()).path.slice(1),
      url: fileLocationSigned,
    };
    const dataSaveUnsignDoc = {
      contract_number: contract_number,
      file_name: file_name,
      file_path: fileLocationUnsigned ? url.parse(fileLocationUnsigned.toString()).path.slice(1) : dataSaveSignedDoc.file_path,
      url: fileLocationUnsigned ? fileLocationUnsigned : dataSaveSignedDoc.url,
    };

    const unsignDoc = await db.loan_unsign_doc.create(dataSaveUnsignDoc);
    const docIdLCT = uuid.v4();
    if (!unsignDoc) {
      throw "save unsign doc fail!";
    }

    const loanContractDocument = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "LCT",
      file_name: file_name,
      file_path: dataSaveSignedDoc.file_path,
      url: dataSaveSignedDoc.url,
      doc_group: null,
    });
    if (!loanContractDocument) {
      throw "save loan contract document fail!";
    }

    await markTaskAsSuccess(task);
    return true;
  } catch (e) {
    console.log({
      functionName: `doGenEvcContractFile`,
      errorMessage: e.message,
    });
    console.error(e);

    await markTaskAsError(task, e?.message);
    return false;
  }
}

async function doGenEvcEsigningContractFile(task: TaskModel) {
  await markTaskAsProcessing(task);

  try {
    const taskBody = JSON.parse(task.body);
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start generate file contract ${contract_number}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/los-united/unsigned-contract", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate file contract ${contract_number} successfully, file location: ${fileLocation}`);
    let dataSaveUnsignDoc = {
      contract_number: contract_number,
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
    };

    const unsignDoc = await db.loan_unsign_doc.create(dataSaveUnsignDoc);
    if (!unsignDoc) {
      throw "save unsign doc fail!";
    }

    await markTaskAsSuccess(task);
    return true;
  } catch (e) {
    console.log({
      functionName: `doGenEvcEsigningContractFile`,
      errorMessage: e.message,
    });
    console.error(e);

    await markTaskAsError(task, e?.message);
    return false;
  }
}

async function doGenEvcTermPolicy(task: TaskModel) {
  await markTaskAsProcessing(task);

  try {
    const taskBody = JSON.parse(task.body);
    const { file_name, full_name, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start generate term policy ${full_name}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/los-united/term-policy", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate temp_policy ${full_name} successfully, file location: ${fileLocation}`);

    await markTaskAsSuccess(task);
    return true;
  } catch (e) {
    console.log({
      functionName: `doGenEvcTermPolicy`,
      errorMessage: e.message,
    });
    console.error(e);

    await markTaskAsError(task, e?.message);
    return false;
  }
}

async function doGenTermPolicyV2(task: TaskModel) {
  await markTaskAsProcessing(task);

  try {
    const taskBody = JSON.parse(task.body);
    const { file_name, full_name, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start generate term policy ${full_name}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/los-united/term-policy-v2", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate temp_policy ${full_name} successfully, file location: ${fileLocation}`);

    await markTaskAsSuccess(task);
    return true;
  } catch (e) {
    console.log({
      functionName: `doGenTermPolicyV2`,
      errorMessage: e.message,
    });
    console.error(e);

    await markTaskAsError(task, e?.message);
    return false;
  }
}
