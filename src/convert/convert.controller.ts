import express, { Request, Response, NextFunction } from "express";
const router = express.Router();
import { getStandardResponse } from "../_helpers/func";
import constants from "../_helpers/constants";
import { authorizeService } from "../_middleware/authorizeAdmin";
const { API_CODE_SUCCESS, API_TEXT_SUCCESS } = constants;
import * as convert from "./convert.service";
// routes
router.post("/add-task", addTask);
router.post("/execute-task", executeTask);
router.post("/reset-task", resetTask);
router.post("/generate", generateDoc);
router.get("/esigning/download", downloadDocEsign);

export default router;

function addTask(req: Request, res: Response, next: NextFunction) {
    convert.addConvertTask(req.body)
        .then((result) =>
            res.json(
                getStandardResponse(
                    API_CODE_SUCCESS,
                    API_TEXT_SUCCESS,
                    result,
                    req
                )
            )
        )
        .catch(next);
}

function executeTask(req: Request, res: Response, next: NextFunction) {
    convert.executeConvertTask(req.body)
        .then((result) =>
            res.json(
                getStandardResponse(
                    API_CODE_SUCCESS,
                    API_TEXT_SUCCESS,
                    result,
                    req
                )
            )
        )
        .catch(next);
}
function resetTask(req: Request, res: Response, next: NextFunction) {
    convert.resetConvertTask(req.body)
        .then((result) =>
            res.json(
                getStandardResponse(
                    API_CODE_SUCCESS,
                    API_TEXT_SUCCESS,
                    result,
                    req
                )
            )
        )
        .catch(next);
}
function generateDoc(req: Request, res: Response, next: NextFunction) {
    convert.generateDoc(req.body)
        .then((result) =>
            res.json(
                getStandardResponse(
                    API_CODE_SUCCESS,
                    API_TEXT_SUCCESS,
                    result,
                    req
                )
            )
        )
        .catch(next);
}

async function downloadDocEsign(
  req: Request,
  res: Response,
  next: NextFunction
) {
  convert
    .addConvertTask(req.body)
    .then((result) =>
      res.json(
        getStandardResponse(API_CODE_SUCCESS, API_TEXT_SUCCESS, result, req)
      )
    )
    .catch(next);
}