import db from "../_helpers/db";
import constants from "../_helpers/constants";
import { download, upload } from "../_helpers/s3";
import { getApi, postApi } from "../_helpers/common";

const uuid = require("uuid");

export async function downloadDocEsign(contractNumber) {
  try {
    if (!contractNumber) {
      throw Error(`missing contract Number`);
    }
    const unsignDoc = await db.esigning.findOne({
      where: {
        contractNumber: contractNumber,
      },
      order: [["id", "desc"]],
    });
    if (!unsignDoc) throw Error(`not found document`);
    const doc = await download(unsignDoc.location);
    return doc.toString("base64");
  } catch (error) {
    console.log(
      `[downloadDocEsign] contractNumber ${contractNumber} Đã có lỗi xảy ra!, error ${error.message}`
    );
    throw error;
  }
}

export async function uploadCustomerSignedContract({
  contractNumber,
  fileData,
}) {
  try {
    if (!contractNumber) {
      throw Error(`missing contract Number`);
    }
    if (!fileData) {
      throw Error(`missing fileData`);
    }
    const document = await db.esigning.findOne({
      where: {
        contractNumber: contractNumber,
      },
      order: [["id", "desc"]],
    });
    if (!document) {
      throw Error(`not found document contract ${contractNumber}`);
    }

    const fileName = `${document.fileName}`;
    const buf = Buffer.from(fileData, "base64");
    const s3Result = await upload(
      fileName,
      buf,
      document.signedPrefix || "/los-united/customer_signed"
    );
    await document.update({
      firsSignedLocation: s3Result.Location,
      updatedAt: new Date(),
      status: constants.SIGN_STATUS.FIRST_SIGN,
    });
    return s3Result.Location;
  } catch (error) {
    console.log(
      `[uploadCustomerSignedContract] contractNumber ${contractNumber} Đã có lỗi xảy ra!, error ${error.message}`
    );
    throw error;
  }
}

export async function callbackTask(contractNumber: string) {
  try {
    if (!contractNumber) {
      throw Error(`missing contract Number`);
    }
    const unsignDoc = await db.esigning.findOne({
      where: {
        contractNumber: contractNumber,
      },
      order: [["id", "desc"]],
    });
    if (!unsignDoc) throw Error(`not found document`);

    let url = "";
    switch (unsignDoc.losType) {
      case constants.LOS_TYPE.MC_LOS: {
        url = `${
          global.basic!.losMcCredit[process.env.HOST_ENV]
        }/los-mc-credit/v1/a2/doc/gen-file-result`;
      }
      case constants.LOS_TYPE.LOS_UNITED: {
        //do nothing
      }
    }
    if (url) {
      const customerSignUrl = global.config?.esigning?.customerSignUrl
        ?.replace("{contractNumber}", unsignDoc.contractNumber)
        ?.replace("{identityCard}", unsignDoc.identityCard);
      const payload = {
        contractNumber: unsignDoc.contractNumber,
        customerSignUrl: customerSignUrl,
        docType: unsignDoc.docType,
        fileLocation: unsignDoc.location
      };
      await postApi(url, payload, {
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
  } catch (error) {
    console.log(
      `[downloadDocEsign] contractNumber ${contractNumber} Đã có lỗi xảy ra!, error ${error.message}`
    );
  }
}
