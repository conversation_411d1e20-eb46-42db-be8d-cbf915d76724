import express, { Request, Response, NextFunction } from "express";
const router = express.Router();
import { getStandardResponse } from "../_helpers/func";
import constants from "../_helpers/constants";
import { authorizeService } from "../_middleware/authorizeAdmin";
const { API_CODE_SUCCESS, API_TEXT_SUCCESS } = constants;
import * as esigning from "./esigning.service";
// routes
router.get("/download", downloadDocEsign);

router.post("/customer/upload-signed", uploadCustomerSignedContract);

async function downloadDocEsign(
  req: Request,
  res: Response,
  next: NextFunction
) {
  esigning
    .downloadDocEsign(req.query.contractNumber)
    .then((result) =>
      res.json(
        getStandardResponse(API_CODE_SUCCESS, API_TEXT_SUCCESS, result, req)
      )
    )
    .catch((e) => {
      res.status(500).json(getStandardResponse(500, e.message, {}, req));
    });
}


async function uploadCustomerSignedContract(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    esigning
      .uploadCustomerSignedContract(req.body)
      .then((result) =>
        res.json(
          getStandardResponse(API_CODE_SUCCESS, API_TEXT_SUCCESS, result, req)
        )
      )
      .catch((e) => {
        res.status(500).json(getStandardResponse(500, e.message, {}, req));
      });
  }

export default router;
