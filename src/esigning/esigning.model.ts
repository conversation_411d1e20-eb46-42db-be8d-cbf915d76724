import { DataTypes, Sequelize } from "sequelize";
import { EsigningModel } from "../types";
import constants from "../_helpers/constants";

export function esigningModel(sequelize: Sequelize) {
  const attributes = {
    location: { type: DataTypes.STRING, allowNull: true, field: "location" },
    fileName: { type: DataTypes.STRING, allowNull: true, field: "file_name" },
    losType: { type: DataTypes.STRING, allowNull: true, field: "los_type" },
    isCallback: {type: DataTypes.BOOLEAN, allowNull: true, field: "is_callback", default: false},
    prefix:  { type: DataTypes.STRING, allowNull: true, field: "prefix" },
    signedPrefix:  { type: DataTypes.STRING, allowNull: true, field: "signed_prefix" },
    identityCard: { type: DataTypes.STRING, allowNull: true, field: "identity_card" },
    docType: { type: DataTypes.STRING, allowNull: true, field: "doc_type" },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: constants.SIGN_STATUS.INIT,
      field: "status",
    },
    contractNumber: { type: DataTypes.STRING, allowNull: true, field: "contract_number" },
    firsSignedLocation: { type: DataTypes.STRING, allowNull: true, field: "first_signed_location" },
    secondSignedLocation: { type: DataTypes.STRING, allowNull: true, field: "second_signed_location" },
    createdAt: { type: DataTypes.DATE, allowNull: true, defaultValue: new Date(), field: "created_at" },
    updatedAt: { type: DataTypes.DATE, allowNull: true, defaultValue: new Date(), field: "updated_at" },
  };

  return sequelize.define<EsigningModel>(
    "esignings",
    attributes,
    {
      tableName: "esignings",
      underscored: true, // ✅ Tự động đổi camelCase thành snake_case
      timestamps: true,  // ✅ Tự động thêm created_at, updated_at nếu chưa có
    }
  );
}
