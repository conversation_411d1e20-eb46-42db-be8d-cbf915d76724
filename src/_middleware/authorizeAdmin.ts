import { Request } from "express-jwt";
import { getStandardResponse } from "../_helpers/func";
import constants from "../_helpers/constants";
import { Response, NextFunction } from "express";

export function authorizeService(req: Request, res: Response, next: NextFunction) {
    try {
        if (req.headers && req.headers.authorization) {
            if (req.headers.authorization === constants.TOKEN_SERVICE) {
                return next()
            }
        }
        return res
            .status(401)
            .json(getStandardResponse(401, "Unauthorized", {}, req));
    } catch (error) {
        next(error)
    }
}