import { getStandardResponse } from "../_helpers/func";
import { __ } from "i18n";
import { Request, Response, NextFunction } from "express";

function errorHandler(err: any, req: Request, res: Response, next: NextFunction) {
    console.log(err)
    switch (true) {
        case typeof err === "string":
            console.log(err);
            // custom application error
            const is404 = err.toLowerCase().endsWith("not found");
            const statusCode = is404 ? 404 : 400;
            return res
                .status(200)
                .json(getStandardResponse(statusCode, err, err, req));
        case err.name === "UnauthorizedError":
            // jwt authentication error
            return res
                .status(401)
                .json(getStandardResponse(401, "Unauthorized", err, req));
        default:
            console.log(err);
            console.log(JSON.stringify(err));
            return res
                .status(500)
                .json(getStandardResponse(500, err.msg || __("an_error_has_occurred"), err, req));
    }
}

export default errorHandler;