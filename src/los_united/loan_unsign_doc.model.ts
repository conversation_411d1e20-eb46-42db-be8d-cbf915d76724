import { DataTypes, Sequelize } from "sequelize";
import { LoanUnsignDocModel } from "../types";

export function loanUnsignDocModel(sequelize: Sequelize) {
    const attributes = {
        contract_number: { type: DataTypes.STRING, allowNull: true},
        file_name: { type: DataTypes.STRING, allowNull: true},
        file_path: { type: DataTypes.STRING, allowNull: true},
        url: { type: DataTypes.STRING, allowNull: true},
    };

    return sequelize.define<LoanUnsignDocModel>("loan_unsign_doc", attributes, {
        tableName: 'loan_unsign_doc',
        timestamps: false,
    });
}
