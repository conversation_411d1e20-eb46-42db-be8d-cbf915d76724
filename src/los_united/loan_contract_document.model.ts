import { DataTypes, Sequelize } from "sequelize";
import {LoanContractDocumentModel} from "../types";

export function loanContractDocumentModel(sequelize: Sequelize) {
    const attributes = {
        contract_number: { type: DataTypes.STRING, allowNull: true},
        doc_id: { type: DataTypes.STRING, allowNull: true},
        doc_type: { type: DataTypes.STRING, allowNull: true},
        file_name: { type: DataTypes.STRING, allowNull: true},
        file_path: { type: DataTypes.STRING, allowNull: true},
        url: { type: DataTypes.STRING, allowNull: true},
        doc_group: { type: DataTypes.STRING, allowNull: true},
    };

    return sequelize.define<LoanContractDocumentModel>("loan_contract_document", attributes, {
        tableName: 'loan_contract_document',
        timestamps: false,
    });
}
