import { DataTypes, Sequelize } from "sequelize";
import { LogTransactionModel } from "../types";

export function logTransactionModel(sequelize: Sequelize) {
    const attributes = {
        apiName: { type: DataTypes.STRING, allowNull: true},
        body: { type: DataTypes.STRING(200550), allowNull: true },
        response: { type: DataTypes.STRING(200550), allowNull: true},
        responseCode: { type: DataTypes.STRING, allowNull: true},
        contractNumber: { type: DataTypes.STRING, allowNull: true},
        message: { type: DataTypes.STRING(2000), allowNull: true },
        des: { type: DataTypes.STRING, allowNull: true },
        timeRun: { type: DataTypes.BIGINT, allowNull: true },
        usersID: { type: DataTypes.INTEGER, allowNull: true },
        ipRequest: { type: DataTypes.STRING, allowNull: true },
        serverHost: { type: DataTypes.STRING, allowNull: true },
    };

    return sequelize.define<LogTransactionModel>("log_transactions", attributes);
}
