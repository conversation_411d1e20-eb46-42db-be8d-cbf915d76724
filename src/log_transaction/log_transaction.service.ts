import { Request, Response, NextFunction } from "express";
import db from "../_helpers/db";
const moment = require("moment");
import constants from "../_helpers/constants";



export async function logger(apiName: string, data: any, des: string, req?: Request & { timestamp?: number, message?: string, responseCode?: number, }) {
    try {
        if(constants.IGNORE_LOG_PATHS.find(e=> req?.originalUrl?.includes(e))){
            return;
        }
        if (
            req &&
            req.originalUrl &&
            (req.originalUrl.indexOf("healthcheck") !== -1 ||
                req.originalUrl.indexOf("readFile") !== -1)
            || (apiName && apiName.includes("contract/file"))
        ) {
            return;
        }

        let serverHost = null;
        let timeRun = null;
        let ipRequest = null;
        let responseCode = null;
        let response = null;
        let usersID = null;
        let createAt = moment();
        let body;

        try {
            body = JSON.stringify(data);
        } catch (err) {
            body = "<PERSON>hông thể chuyển đối tượng thành JSON";
        }
        let message = null;
        let contractNumber = null;

        if (data && data.contractNumber) {
            contractNumber = data.contractNumber
        }
        if (data && data.contract_number) {
            contractNumber = data.contract_number
        }

        if (req) {
            if (des === "Response") {
                timeRun = new Date().getTime() - (req.timestamp || 0);
                responseCode = req.responseCode;
                message = req.message;
                response = body;
                try {
                    body = JSON.stringify(req.body);
                    if(body.length>200550)
                    {
                        body= "Hidden"
                    }
                } catch (err1) {
                    body = "Không thể chuyển đối tượng thành JSON";
                }
            }

            if (req.headers) {
                serverHost = req.headers.host;
                ipRequest = req.headers["x-forwarded-for"];
            }

            // console.log(JSON.stringify({
            //     apiName,
            //     body,
            //     response,
            //     responseCode,
            //     message,
            //     des,
            //     timeRun,
            //     usersID,
            //     ipRequest,
            //     serverHost,
            // }));

            await db.log_transactions.create({
                apiName,
                body,
                response,
                responseCode,
                message,
                des,
                createAt,
                timeRun,
                usersID,
                ipRequest,
                serverHost,
                contractNumber,
            });
        } else {
            // console.log(JSON.stringify({
            //     apiName,
            //     body,
            //     response,
            //     responseCode,
            //     message,
            //     des,
            //     timeRun,
            //     usersID,
            //     ipRequest,
            //     serverHost,
            // }));

            await db.log_transactions.create({
                apiName,
                body,
                response,
                responseCode,
                message,
                des,
                createAt,
                timeRun,
                usersID,
                ipRequest,
                serverHost,
                contractNumber,
            });
        }
    } catch (error) {
        console.log("Hàm logger dòng 93 : \n" + apiName + "\n", error + "");
        try {
            console.log(JSON.stringify({
                apiName,
                body: error + "",
                des: "Gọi log_transaction lỗi!"
            }));
        } catch (e) {
            console.log("Hàm logger dòng 97 : \n", e + "");
        }
    }
}

export async function logRequest(req: Request & { timestamp: number }, res: Response, next: NextFunction) {
    try {
        if (
            req &&
            req.originalUrl &&
            (req.originalUrl.indexOf("healthcheck") !== -1 ||
                req.originalUrl.indexOf("readFile") !== -1 ||
                req.originalUrl.indexOf("firebase_events/insert") !== -1 ||
                req.originalUrl.indexOf("user-contacts/importContacts") !== -1)
        ) {
            next();
        } else {
            req.timestamp = new Date().getTime();
            await logger(req.originalUrl, req.body, "Request", req);
            next();
        }
    } catch (error) {
        console.log("Hàm logRequest dòng 112 : \n", error + "");
        next();
    }
}
