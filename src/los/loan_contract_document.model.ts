import { DataTypes, Sequelize } from "sequelize";
import { LOSLoanContractDocumentModel } from "../types";

export function LOSLoanContractDocumentModel(sequelize: Sequelize) {
  const attributes = {
    created_by: { type: DataTypes.STRING, allowNull: true },
    kunn_contract_number: { type: DataTypes.STRING, allowNull: true },
    contract_number: { type: DataTypes.STRING, allowNull: true },
    doc_id: { type: DataTypes.STRING, allowNull: true },
    doc_type: { type: DataTypes.STRING, allowNull: true },
    creation_time: { type: DataTypes.STRING, allowNull: true },
    file_name: { type: DataTypes.STRING, allowNull: true },
    file_key: { type: DataTypes.STRING, allowNull: true },
    url: { type: DataTypes.STRING, allowNull: true },
    doc_group: { type: DataTypes.STRING, allowNull: true },
    document_no: { type: DataTypes.STRING, allowNull: true },
  };

  return sequelize.define<LOSLoanContractDocumentModel>("loan_contract_document", attributes, {
    tableName: "loan_contract_document",
    timestamps: false,
  });
}
