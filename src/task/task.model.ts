import { DataTypes, Sequelize } from "sequelize";
import { TaskModel } from "../types";
import constants from "../_helpers/constants";

export function taskModel(sequelize: Sequelize) {
    const attributes = {
        task_name: { type: DataTypes.STRING, allowNull: false},
        body: { type: DataTypes.STRING(200550), allowNull: true },
        status: { type: DataTypes.NUMBER, allowNull: false, default: constants.TASK_STATUSES.INIT},
        message: { type: DataTypes.STRING(255), allowNull: true },
        startedAt: { type: DataTypes.DATE, allowNull: true },
        endedAt: { type: DataTypes.DATE, allowNull: true },
    };

    return sequelize.define<TaskModel>("tasks", attributes);
}
