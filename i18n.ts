import { Request, Response, NextFunction } from "express";
import i18n from "i18n";

i18n.configure({
    // setup some locales - other locales default to en silently
    locales: ["vi", "en"],

    // where to store json files - defaults to './locales' relative to modules directory
    directory: "./locales",

    defaultLocale: "vi",
    register: global,
});

const i18nMiddleware = async function (req: Request, res: Response, next: NextFunction) {
    i18n.init(req, res);

    return next();
};

export default i18nMiddleware;
