{"name": "backend", "version": "1.0.0", "description": "Node.js + MySQL", "license": "MIT", "repository": {"type": "git", "url": ""}, "scripts": {"start": "cross-env NODE_ENV=uat DEBUG=true ts-node-dev --respawn -- server.ts", "build": "tsc", "uat": "cross-env NODE_ENV=uat ts-node server.ts", "prod": "cross-env NODE_ENV=production ts-node -T server.ts", "uat-cloud": "cross-env NODE_ENV=uat-cloud ts-node -T server.ts"}, "dependencies": {"@joi/date": "^2.0.1", "angular-expressions": "^1.2.1", "aws-sdk": "^2.968.0", "axios": "^1.7.9", "body-parser": "^1.19.0", "bull": "^4.16.4", "camelcase-keys": "^7.0.2", "cors": "^2.8.5", "docxtemplater": "^3.49.2", "dotenv": "^10.0.0", "easy-template-x": "^3.2.0", "express": "^4.17.1", "express-healthcheck": "^0.1.0", "express-jwt": "^8.4.1", "express-rate-limit": "^6.7.0", "i18n": "^0.13.3", "libreoffice-convert": "^1.3.2", "moment": "^2.29.4", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "pizzip": "^3.1.7", "redis": "^4.3.1", "sequelize": "^6.20.1", "sequelize-typescript": "^2.1.3", "supertest": "^6.1.5", "ts-node": "^10.8.2", "uuid": "^8.3.2", "xlsx-template": "^1.4.4"}, "devDependencies": {"@types/cors": "^2.8.12", "@types/express": "^4.17.13", "@types/express-rate-limit": "^6.0.0", "@types/i18n": "^0.13.3", "@types/node": "^16.11.39", "cross-env": "^7.0.3", "nodemon": "^3.0.1", "sequelize-cli": "^6.2.0", "sequelize-typescript": "^2.1.3", "ts-node-dev": "^2.0.0", "typescript": "^4.7.3"}, "overrides": {}}