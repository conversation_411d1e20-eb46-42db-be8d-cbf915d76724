{"compilerOptions": {"sourceMap": true, "target": "es2017", "outDir": "./dist", "moduleResolution": "node", "module": "commonjs", "emitDecoratorMetadata": true, "experimentalDecorators": true, "esModuleInterop": true, "types": ["node"], "allowJs": true, "checkJs": false, "allowSyntheticDefaultImports": true, "noImplicitAny": false, "resolveJsonModule": true, "skipLibCheck": true}, "include": ["./src", "./api", "*.ts"], "exclude": ["./dist", "./node_modules"]}