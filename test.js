import { convert2push } from "./src/_helpers/func";
import fs from 'fs';

const run = async () => {
  try {
    const taskBody = {
      doc_type: 'LCT',
      //
      is_callback: true,
      los_type: "MC_LOS",
      contract_number: "2200010691",
      file_name: "20250808_2200010691_LCT.pdf",
      file_tem_path: "./template/HDTD_HM_TEMPLATE_FINV.docx",
      contract_data: {
        contract_number: "2200010691",
        tax_id: "*********",
        id_issue_date: "2025-08-08",
        id_issue_place: "Số 1, ngõ 2, phường 3, quận 4, TP.HCM",
        sme_phone_number: "0*********",
        // id_issue_place: "HCM",
        // id_issue_by: "Công an",
        // id_issue_date_str: "08/08/2025",
        // id_issue_place_str: "HCM",
        // id_issue_by_str: "Công an",
      }
    }
    const { file_name, contract_number, file_tem_path, contract_data, los_type, is_callback, doc_type } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start doGenCustomerEsigningFile ${contract_number}`);
    const prefix = taskBody.prefix || "/los-united/unsigned-contract";
    const signedPrefix = taskBody.signedPrefix || "/los-united/signed-contract";
    let fileLocation = await convert2push(file_tem_path, contract_data, prefix, file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }
    console.log(fileLocation);
    const { identity_card } = contract_data;
  } catch (e) {
    console.log(e);
  }
};

run();